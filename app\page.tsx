"use client"

import { useState, useEffect } from "react"
import { useAuth, useUserType, useIsAuthenticated } from "@/lib/auth-context"
import { Search, MapPin, Filter, Star, Heart, MessageCircle, Menu, Bell } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { EmployerFilterSheet } from "@/components/employer-filter-sheet"
import { JobseekerFilterSheet } from "@/components/jobseeker-filter-sheet"
import Link from "next/link"

export default function HomePage() {
  const { user, isLoading, logout } = useAuth()
  const userType = useUserType()
  const isAuthenticated = useIsAuthenticated()

  // 如果用户未登录，默认显示表演者视图
  const currentUserType = userType || "jobseeker"

  // 筛选面板状态
  const [employerFilterOpen, setEmployerFilterOpen] = useState(false)
  const [jobseekerFilterOpen, setJobseekerFilterOpen] = useState(false)

  // 筛选条件状态
  const [employerFilters, setEmployerFilters] = useState<any>({
    location: [],
    gender: "",
    specialties: [],
    ageRange: [18, 45],
    experience: "",
    education: [],
  })

  const [jobseekerFilters, setJobseekerFilters] = useState<any>({
    location: [],
    jobTypes: [],
    salaryRange: [0, 50],
    employmentType: "",
    benefits: [],
  })

  // 处理筛选逻辑
  const handleEmployerFilters = (filters: any) => {
    setEmployerFilters(filters)
    console.log("应用招聘方筛选:", filters)
  }

  const handleJobseekerFilters = (filters: any) => {
    setJobseekerFilters(filters)
    console.log("应用求职者筛选:", filters)
  }

  // 检查是否有活跃的筛选条件
  const hasActiveEmployerFilters = () => {
    return (
      employerFilters.location.length > 0 ||
      employerFilters.gender !== "" ||
      employerFilters.specialties.length > 0 ||
      employerFilters.ageRange[0] !== 18 ||
      employerFilters.ageRange[1] !== 45 ||
      employerFilters.experience !== "" ||
      employerFilters.education.length > 0
    )
  }

  const hasActiveJobseekerFilters = () => {
    return (
      jobseekerFilters.location.length > 0 ||
      jobseekerFilters.jobTypes.length > 0 ||
      jobseekerFilters.salaryRange[0] !== 0 ||
      jobseekerFilters.salaryRange[1] !== 50 ||
      jobseekerFilters.employmentType !== "" ||
      jobseekerFilters.benefits.length > 0
    )
  }

  // 打开对应的筛选面板
  const handleFilterClick = () => {
    if (currentUserType === "employer") {
      setEmployerFilterOpen(true)
    } else {
      setJobseekerFilterOpen(true)
    }
  }

  // 清除筛选条件的辅助函数
  const clearEmployerFilter = (type: string, value?: string) => {
    if (type === "location" && value) {
      setEmployerFilters({
        ...employerFilters,
        location: employerFilters.location.filter((item: string) => item !== value),
      })
    } else if (type === "gender") {
      setEmployerFilters({ ...employerFilters, gender: "" })
    } else if (type === "specialties" && value) {
      setEmployerFilters({
        ...employerFilters,
        specialties: employerFilters.specialties.filter((item: string) => item !== value),
      })
    } else if (type === "ageRange") {
      setEmployerFilters({ ...employerFilters, ageRange: [18, 45] })
    } else if (type === "experience") {
      setEmployerFilters({ ...employerFilters, experience: "" })
    } else if (type === "education" && value) {
      setEmployerFilters({
        ...employerFilters,
        education: employerFilters.education.filter((item: string) => item !== value),
      })
    }
  }

  const clearJobseekerFilter = (type: string, value?: string) => {
    if (type === "location" && value) {
      setJobseekerFilters({
        ...jobseekerFilters,
        location: jobseekerFilters.location.filter((item: string) => item !== value),
      })
    } else if (type === "jobTypes" && value) {
      setJobseekerFilters({
        ...jobseekerFilters,
        jobTypes: jobseekerFilters.jobTypes.filter((item: string) => item !== value),
      })
    } else if (type === "salaryRange") {
      setJobseekerFilters({ ...jobseekerFilters, salaryRange: [0, 50] })
    } else if (type === "employmentType") {
      setJobseekerFilters({ ...jobseekerFilters, employmentType: "" })
    } else if (type === "benefits" && value) {
      setJobseekerFilters({
        ...jobseekerFilters,
        benefits: jobseekerFilters.benefits.filter((item: string) => item !== value),
      })
    }
  }

  // 格式化显示文本的辅助函数
  const formatSalary = (value: number) => {
    if (value === 0) return "不限"
    if (value >= 50) return "50K+"
    return `${value}K`
  }

  const getEmploymentTypeText = (type: string) => {
    switch (type) {
      case "full-time": return "全职"
      case "part-time": return "兼职"
      case "temporary": return "临时"
      case "contract": return "合同制"
      default: return ""
    }
  }

  const getExperienceText = (exp: string) => {
    switch (exp) {
      case "0-1": return "应届/1年以内"
      case "1-3": return "1-3年"
      case "3-5": return "3-5年"
      case "5+": return "5年以上"
      default: return ""
    }
  }

  const performers = [
    {
      id: 1,
      name: "李小华",
      age: 24,
      gender: "女",
      major: "古典舞",
      location: "北京市朝阳区",
      school: "北京舞蹈学院",
      avatar: "/placeholder.svg?height=60&width=60",
      skills: ["古典舞", "民族舞", "芭蕾基础"],
      previewImage: "/placeholder.svg?height=120&width=160",
      rating: 4.8,
      experience: "3年",
    },
    {
      id: 2,
      name: "张明",
      age: 26,
      gender: "男",
      major: "武术表演",
      location: "上海市浦东新区",
      school: "上海戏剧学院",
      avatar: "/placeholder.svg?height=60&width=60",
      skills: ["太极", "长拳", "双节棍", "特技表演"],
      previewImage: "/placeholder.svg?height=120&width=160",
      rating: 4.9,
      experience: "5年",
    },
  ]

  const performanceJobs = [
    {
      id: 1,
      title: "民族舞演员",
      company: "东方歌舞团",
      salary: "8K-15K",
      location: "北京市海淀区",
      requirements: ["民族舞", "3年经验", "形象佳"],
      benefits: ["五险一金", "演出补贴", "舞台机会多"],
      urgent: true,
      publishTime: "2小时前",
    },
    {
      id: 2,
      title: "杂技演员",
      company: "星光马戏团",
      salary: "10K-20K",
      location: "上海市徐汇区",
      requirements: ["杂技", "柔韧性好", "有团队合作经验"],
      benefits: ["国际巡演机会", "提供住宿", "培训机会"],
      urgent: false,
      publishTime: "1天前",
    },
  ]

  const categories = [
    { name: "舞蹈", count: 56, icon: "💃" },
    { name: "表演", count: 43, icon: "🎭" },
    { name: "武术", count: 28, icon: "🥋" },
    { name: "杂技", count: 15, icon: "🤹" },
    { name: "音乐", count: 37, icon: "🎵" },
    { name: "戏曲", count: 22, icon: "🎪" },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="px-4 py-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-80">
                  <SheetHeader className="py-6">
                    <SheetTitle className="text-lg font-semibold text-green-600 mb-6">薏仁直聘</SheetTitle>
                  </SheetHeader>
                  <div className="py-6">
                    <nav className="space-y-4">
                      <Link href="/" className="block py-2 text-gray-700 hover:text-green-600">
                        首页
                      </Link>
                      <Link href="/forum" className="block py-2 text-gray-700 hover:text-green-600">
                        论坛
                      </Link>
                      <Link href="/profile" className="block py-2 text-gray-700 hover:text-green-600">
                        个人中心
                      </Link>
                      <Link href="/messages" className="block py-2 text-gray-700 hover:text-green-600">
                        消息
                      </Link>
                      <Link href="/settings" className="block py-2 text-gray-700 hover:text-green-600">
                        设置
                      </Link>
                      {isAuthenticated && (
                        <button
                          onClick={logout}
                          className="block w-full text-left py-2 text-red-600 hover:text-red-700"
                        >
                          退出登录
                        </button>
                      )}
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>
              <h1 className="text-xl font-bold text-green-600">薏仁直聘</h1>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" className="p-2">
                <Bell className="h-5 w-5" />
              </Button>
              {isAuthenticated ? (
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar || "/placeholder.svg?height=32&width=32"} />
                  <AvatarFallback>{user?.name?.[0] || "我"}</AvatarFallback>
                </Avatar>
              ) : (
                <Button asChild size="sm" variant="outline" className="h-8 px-3 rounded-xl">
                  <Link href="/login">登录</Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="px-4 py-4 pb-20">
        {/* Search Section */}
        <div className="bg-white rounded-2xl shadow-sm p-4 mb-4">
          <div className="relative mb-3">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input placeholder="搜索表演类型、技能..." className="pl-10 h-12 rounded-xl border-gray-200" />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1 h-10 rounded-xl">
              <MapPin className="h-4 w-4 mr-1" />
              位置
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 h-10 rounded-xl"
              onClick={handleFilterClick}
            >
              <Filter className="h-4 w-4 mr-1" />
              筛选
              {((currentUserType === "employer" && hasActiveEmployerFilters()) ||
                (currentUserType === "jobseeker" && hasActiveJobseekerFilters())) && (
                <Badge className="ml-2 bg-green-500 text-white">
                  {currentUserType === "employer"
                    ? employerFilters.location.length +
                      (employerFilters.gender ? 1 : 0) +
                      employerFilters.specialties.length +
                      (employerFilters.ageRange[0] !== 18 || employerFilters.ageRange[1] !== 45 ? 1 : 0) +
                      (employerFilters.experience ? 1 : 0) +
                      employerFilters.education.length
                    : jobseekerFilters.location.length +
                      jobseekerFilters.jobTypes.length +
                      (jobseekerFilters.salaryRange[0] !== 0 || jobseekerFilters.salaryRange[1] !== 50 ? 1 : 0) +
                      (jobseekerFilters.employmentType ? 1 : 0) +
                      jobseekerFilters.benefits.length
                  }
                </Badge>
              )}
            </Button>
            <Button className="px-6 h-10 rounded-xl">搜索</Button>
          </div>
        </div>

        {/* Active Filters Display */}
        {((currentUserType === "employer" && hasActiveEmployerFilters()) ||
          (currentUserType === "jobseeker" && hasActiveJobseekerFilters())) && (
          <div className="bg-white rounded-2xl shadow-sm p-4 mb-4">
            <h3 className="font-medium mb-3">已选条件</h3>
            <div className="flex flex-wrap gap-2">
              {currentUserType === "employer" ? (
                <>
                  {employerFilters.location.map((city: string) => (
                    <Badge key={city} variant="secondary" className="rounded-full px-3 py-1">
                      {city}
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("location", city)}>
                        ×
                      </button>
                    </Badge>
                  ))}

                  {employerFilters.gender && (
                    <Badge variant="secondary" className="rounded-full px-3 py-1">
                      {employerFilters.gender === "male" ? "男" : "女"}
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("gender")}>
                        ×
                      </button>
                    </Badge>
                  )}

                  {employerFilters.specialties.map((specialty: string) => (
                    <Badge key={specialty} variant="secondary" className="rounded-full px-3 py-1">
                      {specialty}
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("specialties", specialty)}>
                        ×
                      </button>
                    </Badge>
                  ))}

                  {(employerFilters.ageRange[0] !== 18 || employerFilters.ageRange[1] !== 45) && (
                    <Badge variant="secondary" className="rounded-full px-3 py-1">
                      {employerFilters.ageRange[0]}-{employerFilters.ageRange[1]}岁
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("ageRange")}>
                        ×
                      </button>
                    </Badge>
                  )}

                  {employerFilters.experience && (
                    <Badge variant="secondary" className="rounded-full px-3 py-1">
                      {getExperienceText(employerFilters.experience)}
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("experience")}>
                        ×
                      </button>
                    </Badge>
                  )}

                  {employerFilters.education.map((edu: string) => (
                    <Badge key={edu} variant="secondary" className="rounded-full px-3 py-1">
                      {edu}
                      <button className="ml-1 text-gray-500" onClick={() => clearEmployerFilter("education", edu)}>
                        ×
                      </button>
                    </Badge>
                  ))}
                </>
              ) : (
                <>
                  {jobseekerFilters.location.map((city: string) => (
                    <Badge key={city} variant="secondary" className="rounded-full px-3 py-1">
                      {city}
                      <button className="ml-1 text-gray-500" onClick={() => clearJobseekerFilter("location", city)}>
                        ×
                      </button>
                    </Badge>
                  ))}

                  {jobseekerFilters.jobTypes.map((type: string) => (
                    <Badge key={type} variant="secondary" className="rounded-full px-3 py-1">
                      {type}
                      <button className="ml-1 text-gray-500" onClick={() => clearJobseekerFilter("jobTypes", type)}>
                        ×
                      </button>
                    </Badge>
                  ))}

                  {(jobseekerFilters.salaryRange[0] !== 0 || jobseekerFilters.salaryRange[1] !== 50) && (
                    <Badge variant="secondary" className="rounded-full px-3 py-1">
                      {formatSalary(jobseekerFilters.salaryRange[0])}-{formatSalary(jobseekerFilters.salaryRange[1])}
                      <button className="ml-1 text-gray-500" onClick={() => clearJobseekerFilter("salaryRange")}>
                        ×
                      </button>
                    </Badge>
                  )}

                  {jobseekerFilters.employmentType && (
                    <Badge variant="secondary" className="rounded-full px-3 py-1">
                      {getEmploymentTypeText(jobseekerFilters.employmentType)}
                      <button className="ml-1 text-gray-500" onClick={() => clearJobseekerFilter("employmentType")}>
                        ×
                      </button>
                    </Badge>
                  )}

                  {jobseekerFilters.benefits.map((benefit: string) => (
                    <Badge key={benefit} variant="secondary" className="rounded-full px-3 py-1">
                      {benefit}
                      <button className="ml-1 text-gray-500" onClick={() => clearJobseekerFilter("benefits", benefit)}>
                        ×
                      </button>
                    </Badge>
                  ))}
                </>
              )}
            </div>
          </div>
        )}

        {/* Categories */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3 px-1">表演类别</h3>
          <div className="grid grid-cols-3 gap-3">
            {categories.map((category) => (
              <div
                key={category.name}
                className="bg-white rounded-2xl p-4 text-center shadow-sm active:scale-95 transition-transform"
              >
                <div className="text-2xl mb-2">{category.icon}</div>
                <div className="text-sm font-medium">{category.name}</div>
                <div className="text-xs text-gray-500">{category.count}个</div>
              </div>
            ))}
          </div>
        </div>

        {/* User Type Display or Login Prompt */}
        {isAuthenticated ? (
          <div className="flex justify-center mb-6">
            <div className="bg-white rounded-2xl p-3 shadow-sm">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user?.avatar || "/placeholder.svg"} />
                  <AvatarFallback>{user?.name?.[0] || "用"}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">{user?.name}</p>
                  <p className="text-xs text-gray-500">
                    {currentUserType === "jobseeker" ? "表演者" : "招募方"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex justify-center mb-6">
            <div className="bg-white rounded-2xl p-4 shadow-sm text-center">
              <p className="text-gray-600 mb-3">登录后查看个性化内容</p>
              <div className="flex space-x-2">
                <Button asChild size="sm" className="rounded-xl">
                  <Link href="/login">登录</Link>
                </Button>
                <Button asChild variant="outline" size="sm" className="rounded-xl">
                  <Link href="/register">注册</Link>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Content Based on User Type */}
        {currentUserType === "jobseeker" ? (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">推荐演出机会</h3>
              <Button variant="ghost" size="sm" className="text-green-600">
                更多
              </Button>
            </div>
            <div className="space-y-4">
              {performanceJobs.map((job) => (
                <Card key={job.id} className="rounded-2xl shadow-sm active:scale-98 transition-transform">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-lg">{job.title}</h4>
                          {job.urgent && <Badge className="bg-red-100 text-red-600 text-xs px-2 py-0.5">急招</Badge>}
                        </div>
                        <p className="text-gray-600 text-sm">{job.company}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-red-500">{job.salary}</div>
                        <div className="text-xs text-gray-500">{job.publishTime}</div>
                      </div>
                    </div>

                    <div className="flex items-center text-gray-500 mb-3">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="text-sm">{job.location}</span>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {job.requirements.slice(0, 3).map((req, index) => (
                        <Badge key={index} variant="secondary" className="text-xs rounded-lg">
                          {req}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex justify-between items-center">
                      <Button size="sm" asChild className="h-9 px-6 rounded-xl">
                        <Link href={`/job/${job.id}`}>查看详情</Link>
                      </Button>
                      <div className="flex space-x-1">
                        <Button size="sm" variant="ghost" className="p-2">
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="p-2">
                          <Star className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">推荐表演者</h3>
              <Button variant="ghost" size="sm" className="text-green-600">
                更多
              </Button>
            </div>
            <div className="space-y-4">
              {performers.map((person) => (
                <Card key={person.id} className="rounded-2xl shadow-sm active:scale-98 transition-transform">
                  <CardContent className="p-4">
                    <div className="flex space-x-4 mb-3">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={person.avatar || "/placeholder.svg"} />
                        <AvatarFallback>{person.name[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-lg">{person.name}</h4>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 text-yellow-500 mr-1" />
                            <span className="text-sm text-gray-600">{person.rating}</span>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm">
                          {person.age}岁 · {person.gender} · {person.experience}经验
                        </p>
                        <p className="text-sm font-medium text-green-600">{person.major}</p>
                      </div>
                    </div>

                    {person.previewImage && (
                      <div className="mb-3">
                        <div className="aspect-[16/9] bg-gray-200 rounded-xl overflow-hidden">
                          <img
                            src={person.previewImage || "/placeholder.svg"}
                            alt={`${person.name}的表演照片`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center text-gray-500 mb-3">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="text-sm">{person.location}</span>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {person.skills.slice(0, 3).map((skill, index) => (
                        <Badge key={index} variant="secondary" className="text-xs rounded-lg">
                          {skill}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex justify-between items-center">
                      <Button size="sm" asChild className="h-9 px-6 rounded-xl">
                        <Link href={`/candidate/${person.id}`}>查看详情</Link>
                      </Button>
                      <div className="flex space-x-1">
                        <Button size="sm" variant="ghost" className="p-2">
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="p-2">
                          <Star className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Filter Panels */}
      <EmployerFilterSheet
        open={employerFilterOpen}
        onOpenChange={setEmployerFilterOpen}
        onApplyFilters={handleEmployerFilters}
      />

      <JobseekerFilterSheet
        open={jobseekerFilterOpen}
        onOpenChange={setJobseekerFilterOpen}
        onApplyFilters={handleJobseekerFilters}
      />

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 safe-area-pb">
        <div className="flex justify-around items-center">
          <Link href="/" className="flex flex-col items-center py-2 text-green-600">
            <div className="w-6 h-6 mb-1">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
              </svg>
            </div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/forum" className="flex flex-col items-center py-2 text-gray-500">
            <div className="w-6 h-6 mb-1">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
              </svg>
            </div>
            <span className="text-xs">论坛</span>
          </Link>
          <Link href="/messages" className="flex flex-col items-center py-2 text-gray-500">
            <div className="w-6 h-6 mb-1">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
              </svg>
            </div>
            <span className="text-xs">消息</span>
          </Link>
          <Link href="/profile" className="flex flex-col items-center py-2 text-gray-500">
            <div className="w-6 h-6 mb-1">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
              </svg>
            </div>
            <span className="text-xs">我的</span>
          </Link>
        </div>
      </nav>
    </div>
  )
}
