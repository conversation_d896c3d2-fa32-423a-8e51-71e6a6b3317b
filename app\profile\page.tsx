"use client"

import { useState } from "react"
import { <PERSON>tings, Edit, Star, Heart, FileText, Briefcase, Crown, MessageCircle, Shield, Upload } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import Link from "next/link"

export default function ProfilePage() {
  const [userType] = useState<"jobseeker" | "employer">("jobseeker")

  const user = {
    name: "张小明",
    avatar: "/placeholder.svg?height=80&width=80",
    title: "前端开发工程师",
    location: "北京市朝阳区",
    isVip: false,
    profileComplete: 85,
    stats: {
      views: 1234,
      likes: 89,
      applications: 12,
    },
  }

  const applications = [
    {
      id: 1,
      position: "前端开发工程师",
      company: "科技创新有限公司",
      salary: "15K-25K",
      status: "面试中",
      appliedAt: "2024-01-15",
    },
    {
      id: 2,
      position: "React开发工程师",
      company: "互联网科技公司",
      salary: "12K-20K",
      status: "已投递",
      appliedAt: "2024-01-10",
    },
    {
      id: 3,
      position: "全栈开发工程师",
      company: "创业公司",
      salary: "18K-30K",
      status: "已拒绝",
      appliedAt: "2024-01-05",
    },
  ]

  const collections = [
    {
      id: 1,
      type: "job",
      title: "Vue.js开发工程师",
      company: "某知名公司",
      salary: "20K-35K",
      savedAt: "2024-01-12",
    },
    {
      id: 2,
      type: "job",
      title: "前端架构师",
      company: "大型互联网公司",
      salary: "30K-50K",
      savedAt: "2024-01-08",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "面试中":
        return "bg-green-100 text-green-800"
      case "已投递":
        return "bg-yellow-100 text-yellow-800"
      case "已拒绝":
        return "bg-red-100 text-red-800"
      case "已录用":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-green-600">薏仁直聘</h1>
              <nav className="hidden md:flex space-x-6">
                <Link href="/" className="text-gray-700 hover:text-green-600">
                  首页
                </Link>
                <Link href="/forum" className="text-gray-700 hover:text-green-600">
                  论坛
                </Link>
                <Link href="/profile" className="text-green-600 font-medium">
                  个人中心
                </Link>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Profile Sidebar */}
          <div className="space-y-6">
            {/* User Info */}
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="relative inline-block">
                    <Avatar className="h-20 w-20 mx-auto">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} />
                      <AvatarFallback className="text-2xl">{user.name[0]}</AvatarFallback>
                    </Avatar>
                    {user.isVip && (
                      <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-1">
                        <Crown className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-semibold text-lg mt-3">{user.name}</h3>
                  <p className="text-gray-600">{user.title}</p>
                  <p className="text-sm text-gray-500 mt-1">{user.location}</p>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span>资料完整度</span>
                      <span>{user.profileComplete}%</span>
                    </div>
                    <Progress value={user.profileComplete} className="h-2" />
                  </div>

                  <Button className="w-full mt-4" size="sm">
                    <Edit className="h-4 w-4 mr-2" />
                    编辑资料
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">数据统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">简历浏览</span>
                  <span className="font-medium">{user.stats.views}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">获得点赞</span>
                  <span className="font-medium">{user.stats.likes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">投递职位</span>
                  <span className="font-medium">{user.stats.applications}</span>
                </div>
              </CardContent>
            </Card>

            {/* VIP */}
            {!user.isVip && (
              <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                <CardContent className="p-6 text-center">
                  <Crown className="h-12 w-12 text-green-500 mx-auto mb-3" />
                  <h4 className="font-medium mb-2">升级VIP会员</h4>
                  <p className="text-sm text-gray-600 mb-4">享受更多特权服务</p>
                  <Button className="w-full bg-green-500 hover:bg-green-600">立即升级</Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs defaultValue="applications" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="applications">
                  <Briefcase className="h-4 w-4 mr-2" />
                  申请记录
                </TabsTrigger>
                <TabsTrigger value="collections">
                  <Star className="h-4 w-4 mr-2" />
                  收藏
                </TabsTrigger>
                <TabsTrigger value="resume">
                  <FileText className="h-4 w-4 mr-2" />
                  简历管理
                </TabsTrigger>
                <TabsTrigger value="settings">
                  <Settings className="h-4 w-4 mr-2" />
                  账户设置
                </TabsTrigger>
              </TabsList>

              {/* Applications */}
              <TabsContent value="applications">
                <Card>
                  <CardHeader>
                    <CardTitle>职位申请记录</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {applications.map((app) => (
                        <div key={app.id} className="border rounded-xl p-4 hover:shadow-sm transition-shadow">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-lg">{app.position}</h4>
                              <p className="text-gray-600">{app.company}</p>
                              <p className="text-red-500 font-medium mt-1">{app.salary}</p>
                              <p className="text-sm text-gray-500 mt-2">申请时间：{app.appliedAt}</p>
                            </div>
                            <div className="flex flex-col items-end space-y-2">
                              <Badge className={getStatusColor(app.status)}>{app.status}</Badge>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline">
                                  <MessageCircle className="h-4 w-4 mr-1" />
                                  沟通
                                </Button>
                                <Button size="sm" variant="outline">
                                  查看详情
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Collections */}
              <TabsContent value="collections">
                <Card>
                  <CardHeader>
                    <CardTitle>我的收藏</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {collections.map((item) => (
                        <div key={item.id} className="border rounded-xl p-4 hover:shadow-sm transition-shadow">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-lg">{item.title}</h4>
                              <p className="text-gray-600">{item.company}</p>
                              <p className="text-red-500 font-medium mt-1">{item.salary}</p>
                              <p className="text-sm text-gray-500 mt-2">收藏时间：{item.savedAt}</p>
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Heart className="h-4 w-4 mr-1" />
                                取消收藏
                              </Button>
                              <Button size="sm">查看详情</Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Resume */}
              <TabsContent value="resume">
                <Card>
                  <CardHeader>
                    <CardTitle>简历管理</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h4 className="font-medium mb-2">上传简历</h4>
                        <p className="text-gray-600 mb-4">支持PDF、Word格式，文件大小不超过5MB</p>
                        <Button>选择文件</Button>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-medium">我的简历</h4>
                        <div className="border rounded-xl p-4">
                          <div className="flex justify-between items-center">
                            <div>
                              <h5 className="font-medium">张小明_前端开发工程师.pdf</h5>
                              <p className="text-sm text-gray-500">上传时间：2024-01-10</p>
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                预览
                              </Button>
                              <Button size="sm" variant="outline">
                                下载
                              </Button>
                              <Button size="sm" variant="outline">
                                删除
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">我的表演照片</h4>

                      {/* 图片分类管理 */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="p-4">
                          <h5 className="font-medium mb-2">舞台照片</h5>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">上传舞台表演照片</p>
                          </div>
                          <div className="mt-2 space-y-2">
                            <div className="flex justify-between items-center text-sm">
                              <span>《洛神赋》表演.jpg</span>
                              <Button size="sm" variant="outline">
                                删除
                              </Button>
                            </div>
                            <div className="flex justify-between items-center text-sm">
                              <span>民族舞表演.jpg</span>
                              <Button size="sm" variant="outline">
                                删除
                              </Button>
                            </div>
                          </div>
                        </Card>

                        <Card className="p-4">
                          <h5 className="font-medium mb-2">训练照片</h5>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">上传训练照片</p>
                          </div>
                          <div className="mt-2 space-y-2">
                            <div className="flex justify-between items-center text-sm">
                              <span>基本功练习.jpg</span>
                              <Button size="sm" variant="outline">
                                删除
                              </Button>
                            </div>
                          </div>
                        </Card>

                        <Card className="p-4">
                          <h5 className="font-medium mb-2">获奖照片</h5>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">上传获奖照片</p>
                          </div>
                          <div className="mt-2 space-y-2">
                            <div className="flex justify-between items-center text-sm">
                              <span>全国比赛金奖.jpg</span>
                              <Button size="sm" variant="outline">
                                删除
                              </Button>
                            </div>
                          </div>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Settings */}
              <TabsContent value="settings">
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>账户安全</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">登录密码</h5>
                          <p className="text-sm text-gray-500">定期更换密码可以提高账户安全性</p>
                        </div>
                        <Button variant="outline" size="sm">
                          修改
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">手机号码</h5>
                          <p className="text-sm text-gray-500">138****8888</p>
                        </div>
                        <Button variant="outline" size="sm">
                          更换
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">邮箱地址</h5>
                          <p className="text-sm text-gray-500">zhang****@example.com</p>
                        </div>
                        <Button variant="outline" size="sm">
                          更换
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>隐私设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">简历可见性</h5>
                          <p className="text-sm text-gray-500">控制谁可以查看您的简历</p>
                        </div>
                        <Button variant="outline" size="sm">
                          设置
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">求职状态</h5>
                          <p className="text-sm text-gray-500">当前状态：正在求职</p>
                        </div>
                        <Button variant="outline" size="sm">
                          修改
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>其他设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">用户协议</h5>
                          <p className="text-sm text-gray-500">查看平台使用协议</p>
                        </div>
                        <Button variant="outline" size="sm">
                          查看
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">隐私政策</h5>
                          <p className="text-sm text-gray-500">了解我们如何保护您的隐私</p>
                        </div>
                        <Button variant="outline" size="sm">
                          查看
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">客服中心</h5>
                          <p className="text-sm text-gray-500">遇到问题？联系我们的客服团队</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <MessageCircle className="h-4 w-4 mr-1" />
                          联系客服
                        </Button>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium">举报中心</h5>
                          <p className="text-sm text-gray-500">举报违规内容或用户</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <Shield className="h-4 w-4 mr-1" />
                          举报
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  )
}
